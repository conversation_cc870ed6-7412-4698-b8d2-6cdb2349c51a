// Enhanced product data with more details
const products = [
    {
        id: 1,
        name: "جلابة ملكية فاخرة",
        category: "jellaba",
        price: 1500,
        originalPrice: 1800,
        image: "images/jellaba1.jpg",
        description: "جلابة مغربية تقليدية مصنوعة من أجود الأقمشة مع تطريز يدوي فاخر بخيوط الذهب والفضة",
        rating: 4.8,
        badge: "الأكثر مبيعاً",
        colors: ["أزرق ملكي", "أحمر قرمزي", "أخضر زمردي"],
        sizes: ["S", "M", "L", "XL"],
        material: "حرير طبيعي مع تطريز يدوي"
    },
    {
        id: 2,
        name: "جلابة عصرية أنيقة",
        category: "jellaba",
        price: 950,
        originalPrice: null,
        image: "images/jellaba2.jpg",
        description: "تصميم عصري يجمع بين الأصالة والحداثة، مناسبة للمناسبات اليومية والرسمية",
        rating: 4.5,
        badge: null,
        colors: ["بيج", "رمادي", "أسود"],
        sizes: ["S", "M", "L", "XL"],
        material: "قطن مخلوط عالي الجودة"
    },
    {
        id: 3,
        name: "جلابة شتوية دافئة",
        category: "jellaba",
        price: 1200,
        originalPrice: 1400,
        image: "images/jellaba3.jpg",
        description: "جلابة شتوية مبطنة بالصوف الطبيعي لدفء استثنائي مع تصميم أنيق ومريح",
        rating: 4.7,
        badge: "جديد",
        colors: ["بني", "رمادي داكن", "أزرق داكن"],
        sizes: ["M", "L", "XL", "XXL"],
        material: "صوف طبيعي مع بطانة قطنية"
    },
    {
        id: 4,
        name: "قفطان ملكي مطرز",
        category: "kaftan",
        price: 2500,
        originalPrice: 3000,
        image: "images/kaftan1.jpg",
        description: "قفطان فاخر مطرز بخيوط الذهب والأحجار الكريمة، مثالي للمناسبات الخاصة والأعراس",
        rating: 5.0,
        badge: "حصري",
        colors: ["ذهبي", "فضي", "أحمر ملكي"],
        sizes: ["S", "M", "L"],
        material: "ساتان حريري مع تطريز بخيوط الذهب"
    },
    {
        id: 5,
        name: "قفطان عصري أنيق",
        category: "kaftan",
        price: 1800,
        originalPrice: null,
        image: "images/kaftan2.jpg",
        description: "تصميم عصري بألوان زاهية وقصة مريحة للإطلالات اليومية الأنيقة والمناسبات الخاصة",
        rating: 4.6,
        badge: null,
        colors: ["وردي", "أزرق فاتح", "أخضر نعناعي"],
        sizes: ["S", "M", "L", "XL"],
        material: "شيفون عالي الجودة"
    },
    {
        id: 6,
        name: "قفطان سهرة فاخر",
        category: "kaftan",
        price: 3200,
        originalPrice: 3800,
        image: "images/kaftan3.jpg",
        description: "قفطان سهرة بتطريز فاخر وأحجار كريمة للمناسبات الراقية والحفلات الخاصة",
        rating: 4.9,
        badge: "الأكثر مبيعاً",
        colors: ["أسود", "أزرق ملكي", "بنفسجي"],
        sizes: ["S", "M", "L"],
        material: "مخمل فاخر مع تطريز بالأحجار"
    },
    {
        id: 7,
        name: "عباية كلاسيكية أنيقة",
        category: "abaya",
        price: 800,
        originalPrice: 950,
        image: "images/abaya1.jpg",
        description: "عباية كلاسيكية بقصة مريحة وتصميم أنيق للاستخدام اليومي مع لمسة من الأناقة",
        rating: 4.4,
        badge: null,
        colors: ["أسود", "رمادي", "بني"],
        sizes: ["S", "M", "L", "XL"],
        material: "كريب جورجيت"
    },
    {
        id: 8,
        name: "عباية مطرزة فاخرة",
        category: "abaya",
        price: 1400,
        originalPrice: 1600,
        image: "images/abaya2.jpg",
        description: "عباية مطرزة بتفاصيل دقيقة وخامات عالية الجودة مع تطريز يدوي رائع",
        rating: 4.7,
        badge: "جديد",
        colors: ["أسود مع ذهبي", "أزرق داكن", "بنفسجي"],
        sizes: ["S", "M", "L", "XL"],
        material: "كريب ثقيل مع تطريز يدوي"
    },
    {
        id: 9,
        name: "عباية سهرة راقية",
        category: "abaya",
        price: 1900,
        originalPrice: null,
        image: "images/abaya3.jpg",
        description: "عباية سهرة بتصميم راقي مناسبة للمناسبات الخاصة",
        rating: 4.8,
        badge: "حصري"
    },
    {
        id: 10,
        name: "جلابة صيفية خفيفة",
        category: "jellaba",
        price: 750,
        originalPrice: 900,
        image: "images/jellaba4.jpg",
        description: "جلابة صيفية من القطن الخالص، خفيفة ومريحة للطقس الحار",
        rating: 4.3,
        badge: null
    },
    {
        id: 11,
        name: "قفطان مغربي تراثي",
        category: "kaftan",
        price: 2200,
        originalPrice: 2600,
        image: "images/kaftan4.jpg",
        description: "قفطان تراثي بنقوش مغربية أصيلة وألوان تقليدية جميلة",
        rating: 4.8,
        badge: "تراثي"
    },
    {
        id: 12,
        name: "عباية عملية مريحة",
        category: "abaya",
        price: 650,
        originalPrice: null,
        image: "images/abaya4.jpg",
        description: "عباية عملية بتصميم بسيط ومريح للاستخدام اليومي",
        rating: 4.2,
        badge: null
    }
];

let filteredProducts = [...products];

// DOM elements
const productsGrid = document.getElementById('productsGrid');
const categoryFilter = document.getElementById('categoryFilter');
const priceFilter = document.getElementById('priceFilter');

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    displayProducts(products);
    setupEventListeners();
});

// Display products
function displayProducts(productsToShow) {
    if (productsToShow.length === 0) {
        productsGrid.innerHTML = `
            <div class="no-products">
                <i class="fas fa-search" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
                <p>لم يتم العثور على منتجات تطابق المعايير المحددة</p>
            </div>
        `;
        return;
    }

    productsGrid.innerHTML = productsToShow.map(product => `
        <div class="product-card" data-category="${product.category}" data-price="${product.price}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" onerror="this.src='images/placeholder.jpg'">
                ${product.badge ? `<div class="product-badge">${product.badge}</div>` : ''}
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-details">
                    <div class="price-container">
                        ${product.originalPrice ? `<span class="original-price">${product.originalPrice} درهم</span>` : ''}
                        <span class="price">${product.price} درهم</span>
                    </div>
                    <div class="product-rating">
                        ${generateStars(product.rating)}
                        <span>(${product.rating})</span>
                    </div>
                </div>
                <button class="whatsapp-btn" data-product="${product.name}" data-price="${product.price}">
                    <i class="fab fa-whatsapp"></i>
                    اطلب عبر واتساب
                </button>
            </div>
        </div>
    `).join('');

    // Add WhatsApp functionality to new buttons
    setupWhatsAppButtons();
}

// Generate star rating
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star"></i>';
    }
    
    return stars;
}

// Setup event listeners
function setupEventListeners() {
    categoryFilter.addEventListener('change', filterProducts);
    priceFilter.addEventListener('change', filterProducts);
}

// Filter products
function filterProducts() {
    const selectedCategory = categoryFilter.value;
    const selectedPriceRange = priceFilter.value;
    
    filteredProducts = products.filter(product => {
        const categoryMatch = selectedCategory === 'all' || product.category === selectedCategory;
        const priceMatch = checkPriceRange(product.price, selectedPriceRange);
        
        return categoryMatch && priceMatch;
    });
    
    // Show loading animation
    productsGrid.innerHTML = `
        <div class="loading">
            <div class="spinner"></div>
            <p>جاري تحميل المنتجات...</p>
        </div>
    `;
    
    // Simulate loading delay for better UX
    setTimeout(() => {
        displayProducts(filteredProducts);
    }, 500);
}

// Check price range
function checkPriceRange(price, range) {
    if (range === 'all') return true;
    
    switch (range) {
        case '0-1000':
            return price < 1000;
        case '1000-2000':
            return price >= 1000 && price <= 2000;
        case '2000+':
            return price > 2000;
        default:
            return true;
    }
}

// Setup WhatsApp buttons
function setupWhatsAppButtons() {
    const whatsappButtons = document.querySelectorAll('.whatsapp-btn');
    
    whatsappButtons.forEach(button => {
        button.addEventListener('click', function() {
            const product = this.getAttribute('data-product');
            const price = this.getAttribute('data-price');
            
            const message = `مرحبا، أريد طلب ${product} بسعر ${price} درهم. يرجى تأكيد التوفر وتفاصيل التوصيل.`;
            const whatsappUrl = `https://wa.me/212XXXXXXXXX?text=${encodeURIComponent(message)}`;
            
            window.open(whatsappUrl, '_blank');
        });
    });
}
