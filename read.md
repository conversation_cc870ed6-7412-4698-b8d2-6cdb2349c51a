 a Moroccan fashion e-commerce website called "مودا ماروك" (Moda Maroc). 

Project Overview:

Arabic RTL website for selling traditional Moroccan clothing
Features traditional items like جلابة (jellaba), قفطان (kaftan), and عباية (abaya)
WhatsApp-based ordering system (no traditional cart/checkout)
Key Features:

Homepage (`index.html`) with hero section, product showcase, features, testimonials, and contact
Dedicated products page (`products.html`) with filtering by category and price
Responsive design with Arabic typography using Cairo font
Golden color scheme (#d4af37) representing luxury
Floating WhatsApp button for instant customer contact
Technical Stack:

Pure HTML/CSS/JavaScript (no frameworks)
Font Awesome icons
Google Fonts (Cairo)
Mobile-responsive grid layouts
Business Model:

Direct WhatsApp ordering with pre-filled messages
Cash on delivery payment
Free shipping across Morocco
Customer testimonials and social proof
The site emphasizes traditional Moroccan craftsmanship with modern web design, targeting customers who prefer personal communication over automated e-commerce flows.

----------------------------------------------------------------------------

create the products page with filtering functionality and add more product data

----------------------------------------------------------------------------

Add demo content and images to make the site look professional






