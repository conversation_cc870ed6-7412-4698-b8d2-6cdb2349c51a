.products-section {
    padding: 120px 0 80px;
    min-height: 100vh;
}

.products-section h1 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

/* Filters */
.filters {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #333;
}

.filter-group select {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s;
}

.filter-group select:focus {
    outline: none;
    border-color: #d4af37;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #d4af37;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
}

.product-card h3 {
    font-size: 1.4rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.product-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.product-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.price {
    font-size: 1.6rem;
    color: #d4af37;
    font-weight: 700;
}

.original-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
    margin-right: 0.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: #ffc107;
}

.whatsapp-btn {
    width: 100%;
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    border: none;
    padding: 15px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    transform: translateY(-2px);
}

.no-products {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-size: 1.2rem;
}

/* Loading animation */
.loading {
    text-align: center;
    padding: 3rem;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #d4af37;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .filters {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .products-section h1 {
        font-size: 2rem;
    }
}